//
//  avdome.m
//  真后台
//
//  Created by 小七 on 2025/7/30.
//

#import "avdome.h"

@implementation avdome

- (instancetype)init {
    self = [super init];
    if (self) {
        _isInBackground = NO;
        [self setupAudioSession];
        [self setupNotifications];
    }
    return self;
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
    [self stopBackgroundAudio];
}

#pragma mark - 音频会话设置
- (void)setupAudioSession {
    NSError *error = nil;
    AVAudioSession *audioSession = [AVAudioSession sharedInstance];

    // 设置音频会话类别为播放，允许后台播放
    [audioSession setCategory:AVAudioSessionCategoryPlayback
                  withOptions:AVAudioSessionCategoryOptionMixWithOthers
                        error:&error];

    if (error) {
        NSLog(@"设置音频会话失败: %@", error.localizedDescription);
        return;
    }

    // 激活音频会话
    [audioSession setActive:YES error:&error];
    if (error) {
        NSLog(@"激活音频会话失败: %@", error.localizedDescription);
    }
}

#pragma mark - 通知设置
- (void)setupNotifications {
    NSNotificationCenter *center = [NSNotificationCenter defaultCenter];

    // 监听应用进入后台
    [center addObserver:self
               selector:@selector(applicationDidEnterBackground:)
                   name:UIApplicationDidEnterBackgroundNotification
                 object:nil];

    // 监听应用进入前台
    [center addObserver:self
               selector:@selector(applicationWillEnterForeground:)
                   name:UIApplicationWillEnterForegroundNotification
                 object:nil];

    // 监听应用变为活跃状态
    [center addObserver:self
               selector:@selector(applicationDidBecomeActive:)
                   name:UIApplicationDidBecomeActiveNotification
                 object:nil];
}

#pragma mark - 应用状态变化处理
- (void)applicationDidEnterBackground:(NSNotification *)notification {
    NSLog(@"应用进入后台，开始播放无声音频");
    self.isInBackground = YES;
    [self startBackgroundAudio];
}

- (void)applicationWillEnterForeground:(NSNotification *)notification {
    NSLog(@"应用即将进入前台");
}

- (void)applicationDidBecomeActive:(NSNotification *)notification {
    NSLog(@"应用变为活跃状态，停止播放无声音频");
    self.isInBackground = NO;
    [self stopBackgroundAudio];
}

#pragma mark - 音频播放控制
- (void)startBackgroundAudio {
    if (self.audioPlayer && self.audioPlayer.isPlaying) {
        return; // 已经在播放了
    }

    // 获取app bundle中的无声音效文件路径
    NSString *silentAudioPath = [[NSBundle mainBundle] pathForResource:@"1分钟无声空白音效" ofType:@"mp3"];

    if (!silentAudioPath) {
        NSLog(@"找不到无声音效文件: 1分钟无声空白音效.mp3");
        return;
    }

    NSURL *audioURL = [NSURL fileURLWithPath:silentAudioPath];
    NSError *error = nil;
    self.audioPlayer = [[AVAudioPlayer alloc] initWithContentsOfURL:audioURL error:&error];

    if (error) {
        NSLog(@"创建音频播放器失败: %@", error.localizedDescription);
        return;
    }

    self.audioPlayer.delegate = self;
    self.audioPlayer.numberOfLoops = -1; // 无限循环
    self.audioPlayer.volume = 0.0; // 静音

    BOOL success = [self.audioPlayer play];
    if (success) {
        NSLog(@"开始播放无声音频文件: %@", silentAudioPath);
    } else {
        NSLog(@"播放无声音频失败");
    }
}

- (void)stopBackgroundAudio {
    if (self.audioPlayer && self.audioPlayer.isPlaying) {
        [self.audioPlayer stop];
        NSLog(@"停止播放无声音频");
    }
    self.audioPlayer = nil;
}



#pragma mark - AVAudioPlayerDelegate
- (void)audioPlayerDidFinishPlaying:(AVAudioPlayer *)player successfully:(BOOL)flag {
    NSLog(@"音频播放完成: %@", flag ? @"成功" : @"失败");

    // 如果还在后台，重新开始播放
    if (self.isInBackground) {
        [self startBackgroundAudio];
    }
}

- (void)audioPlayerDecodeErrorDidOccur:(AVAudioPlayer *)player error:(NSError *)error {
    NSLog(@"音频解码错误: %@", error.localizedDescription);
}

@end
