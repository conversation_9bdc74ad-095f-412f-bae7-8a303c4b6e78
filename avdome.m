//
//  avdome.m
//  真后台
//
//  Created by 小七 on 2025/7/30.
//

#import "avdome.h"

@implementation avdome

#pragma mark - 类方法
+ (void)load {
    // 在类加载时自动初始化后台音频管理器
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        [avdome sharedInstance];
        NSLog(@"后台音频管理器已自动加载");
    });
}

+ (instancetype)sharedInstance {
    static avdome *sharedInstance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        sharedInstance = [[self alloc] init];
    });
    return sharedInstance;
}

#pragma mark - 初始化
- (instancetype)init {
    self = [super init];
    if (self) {
        _isInBackground = NO;
        [self setupAudioSession];
        [self setupNotifications];
        NSLog(@"后台音频管理器初始化完成");
    }
    return self;
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
    [self stopBackgroundAudio];
    [self stopProgressTimer];
}

#pragma mark - 音频会话设置
- (void)setupAudioSession {
    NSError *error = nil;
    AVAudioSession *audioSession = [AVAudioSession sharedInstance];

    NSLog(@"开始设置音频会话...");

    // 设置音频会话类别为播放，允许后台播放
    BOOL categorySuccess = [audioSession setCategory:AVAudioSessionCategoryPlayback
                                          withOptions:AVAudioSessionCategoryOptionMixWithOthers
                                                error:&error];

    if (error || !categorySuccess) {
        NSLog(@"设置音频会话类别失败: %@", error.localizedDescription);
        return;
    }
    NSLog(@"音频会话类别设置成功: %@", audioSession.category);

    // 激活音频会话
    BOOL activeSuccess = [audioSession setActive:YES error:&error];
    if (error || !activeSuccess) {
        NSLog(@"激活音频会话失败: %@", error.localizedDescription);
        return;
    }
    NSLog(@"音频会话激活成功");

    // 检查音频会话状态
    NSLog(@"当前音频会话状态:");
    NSLog(@"- 类别: %@", audioSession.category);
    NSLog(@"- 选项: %lu", (unsigned long)audioSession.categoryOptions);
    NSLog(@"- 是否激活: %@", audioSession.isOtherAudioPlaying ? @"其他音频播放中" : @"可用");
}

#pragma mark - 通知设置
- (void)setupNotifications {
    NSNotificationCenter *center = [NSNotificationCenter defaultCenter];

    // 监听应用进入后台
    [center addObserver:self
               selector:@selector(applicationDidEnterBackground:)
                   name:UIApplicationDidEnterBackgroundNotification
                 object:nil];

    // 监听应用进入前台
    [center addObserver:self
               selector:@selector(applicationWillEnterForeground:)
                   name:UIApplicationWillEnterForegroundNotification
                 object:nil];

    // 监听应用变为活跃状态
    [center addObserver:self
               selector:@selector(applicationDidBecomeActive:)
                   name:UIApplicationDidBecomeActiveNotification
                 object:nil];
}

#pragma mark - 应用状态变化处理
- (void)applicationDidEnterBackground:(NSNotification *)notification {
    NSLog(@"应用进入后台，开始播放无声音频");
    self.isInBackground = YES;
    [self startBackgroundAudio];
}

- (void)applicationWillEnterForeground:(NSNotification *)notification {
    NSLog(@"应用即将进入前台");
}

- (void)applicationDidBecomeActive:(NSNotification *)notification {
    NSLog(@"应用变为活跃状态，停止播放无声音频");
    self.isInBackground = NO;
    [self stopBackgroundAudio];
}

#pragma mark - 音频播放控制
- (void)startBackgroundAudio {
    if (self.audioPlayer && self.audioPlayer.isPlaying) {
        return; // 已经在播放了
    }

    // 获取app bundle中的无声音效文件路径
    NSString *silentAudioPath = [[NSBundle mainBundle] pathForResource:@"1分钟无声空白音效" ofType:@"mp3"];

    if (!silentAudioPath) {
        NSLog(@"找不到无声音效文件: 1分钟无声空白音效.mp3");
        return;
    }

    NSURL *audioURL = [NSURL fileURLWithPath:silentAudioPath];
    NSError *error = nil;
    self.audioPlayer = [[AVAudioPlayer alloc] initWithContentsOfURL:audioURL error:&error];

    if (error) {
        NSLog(@"创建音频播放器失败: %@", error.localizedDescription);
        return;
    }

    self.audioPlayer.delegate = self;
    self.audioPlayer.numberOfLoops = -1; // 无限循环
    self.audioPlayer.volume = 0.01; // 极小音量而不是完全静音

    // 准备播放
    BOOL prepared = [self.audioPlayer prepareToPlay];
    NSLog(@"音频准备播放: %@", prepared ? @"成功" : @"失败");

    BOOL success = [self.audioPlayer play];
    NSLog(@"播放状态: %@", success ? @"成功" : @"失败");
    NSLog(@"是否正在播放: %@", self.audioPlayer.isPlaying ? @"是" : @"否");
    NSLog(@"当前音量: %.3f", self.audioPlayer.volume);
    NSLog(@"循环次数: %ld", (long)self.audioPlayer.numberOfLoops);

    if (success && self.audioPlayer.isPlaying) {
        NSLog(@"开始播放无声音频文件: %@", silentAudioPath);
        NSLog(@"音频文件总时长: %.2f秒", self.audioPlayer.duration);
        [self startProgressTimer];
    } else {
        NSLog(@"播放无声音频失败 - 音频播放器状态异常");
        NSLog(@"错误排查: 文件路径=%@, 准备状态=%@, 播放状态=%@",
              silentAudioPath, prepared ? @"OK" : @"FAIL", success ? @"OK" : @"FAIL");
    }
}

- (void)stopBackgroundAudio {
    [self stopProgressTimer];
    if (self.audioPlayer && self.audioPlayer.isPlaying) {
        [self.audioPlayer stop];
        NSLog(@"停止播放无声音频");
    }
    self.audioPlayer = nil;
}

#pragma mark - 进度监控
- (void)startProgressTimer {
    [self stopProgressTimer]; // 先停止之前的定时器

    // 每秒更新一次进度
    self.progressTimer = [NSTimer scheduledTimerWithTimeInterval:1.0
                                                          target:self
                                                        selector:@selector(updateProgress)
                                                        userInfo:nil
                                                         repeats:YES];
    NSLog(@"开始监控播放进度");
}

- (void)stopProgressTimer {
    if (self.progressTimer) {
        [self.progressTimer invalidate];
        self.progressTimer = nil;
        NSLog(@"停止监控播放进度");
    }
}

- (void)updateProgress {
    if (self.audioPlayer) {
        NSTimeInterval currentTime = self.audioPlayer.currentTime;
        NSTimeInterval duration = self.audioPlayer.duration;
        BOOL isPlaying = self.audioPlayer.isPlaying;

        NSLog(@"音频播放器状态检查:");
        NSLog(@"- 是否正在播放: %@", isPlaying ? @"是" : @"否");
        NSLog(@"- 当前时间: %.2f秒", currentTime);
        NSLog(@"- 总时长: %.2f秒", duration);
        NSLog(@"- 音量: %.3f", self.audioPlayer.volume);

        if (isPlaying && duration > 0) {
            // 计算播放进度百分比
            double progressPercentage = (currentTime / duration) * 100.0;

            // 格式化时间显示
            int minutes = (int)currentTime / 60;
            int seconds = (int)currentTime % 60;
            int totalMinutes = (int)duration / 60;
            int totalSeconds = (int)duration % 60;

            NSLog(@"播放进度: %02d:%02d / %02d:%02d (%.1f%%) - 已播放%.2f秒",
                  minutes, seconds, totalMinutes, totalSeconds, progressPercentage, currentTime);
        } else {
            NSLog(@"⚠️ 音频播放器未在播放状态！");

            // 尝试重新播放
            if (self.isInBackground && !isPlaying) {
                NSLog(@"尝试重新启动播放...");
                BOOL restartSuccess = [self.audioPlayer play];
                NSLog(@"重新播放结果: %@", restartSuccess ? @"成功" : @"失败");
            }
        }
    } else {
        NSLog(@"⚠️ 音频播放器为空！");
    }
}

#pragma mark - AVAudioPlayerDelegate
- (void)audioPlayerDidFinishPlaying:(AVAudioPlayer *)player successfully:(BOOL)flag {
    NSLog(@"音频播放完成: %@", flag ? @"成功" : @"失败");

    // 如果还在后台，重新开始播放
    if (self.isInBackground) {
        [self startBackgroundAudio];
    }
}

- (void)audioPlayerDecodeErrorDidOccur:(AVAudioPlayer *)player error:(NSError *)error {
    NSLog(@"音频解码错误: %@", error.localizedDescription);
}

@end
