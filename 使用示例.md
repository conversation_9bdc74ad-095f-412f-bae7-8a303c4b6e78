# 后台音频播放使用示例

## 功能说明
这个 `avdome` 类实现了无限循环无声播放MP3文件来保持app在后台运行的功能。

## 主要特性
1. **自动加载**: 使用 `+load` 方法在类加载时自动初始化，无需手动创建实例
2. **自动检测应用状态**: 当应用进入后台时自动开始播放无声音频
3. **前台自动停止**: 当应用回到前台时立即停止播放
4. **无声播放**: 音量设置为0，不会影响用户体验
5. **无限循环**: 使用 `numberOfLoops = -1` 实现无限循环播放
6. **使用MP3文件**: 读取app bundle中的 `1分钟无声空白音效.mp3` 文件
7. **单例模式**: 确保全局只有一个实例

## 使用方法

### 1. 自动加载（推荐）

只需要在项目中包含 `avdome.h` 和 `avdome.m` 文件，类会在应用启动时自动加载：

```objc
// 无需任何代码，avdome 会在 +load 方法中自动初始化
// 只需要确保文件被包含在项目中即可
```

### 1.1 手动获取实例（可选）

如果需要手动控制，可以获取单例实例：

```objc
#import "avdome.h"

// 获取单例实例
avdome *audioManager = [avdome sharedInstance];
```

### 2. 添加音频文件到项目

将 `1分钟无声空白音效.mp3` 文件添加到你的Xcode项目的bundle中：
1. 在Xcode中右键点击项目
2. 选择 "Add Files to [项目名]"
3. 选择 `1分钟无声空白音效.mp3` 文件
4. 确保 "Add to target" 勾选了你的app target

### 3. 在 Info.plist 中添加后台模式权限

在你的 Info.plist 文件中添加以下配置：

```xml
<key>UIBackgroundModes</key>
<array>
    <string>audio</string>
</array>
```

### 4. 手动控制（可选）

如果你需要手动控制播放：

```objc
// 获取单例实例
avdome *audioManager = [avdome sharedInstance];

// 开始后台播放
[audioManager startBackgroundAudio];

// 停止后台播放
[audioManager stopBackgroundAudio];
```

## 工作原理

1. **自动加载**: `+load` 方法在类加载时自动调用，创建单例实例
2. **音频会话设置**: 使用 `AVAudioSessionCategoryPlayback` 类别允许后台播放
3. **应用状态监听**: 监听 `UIApplicationDidEnterBackgroundNotification` 和 `UIApplicationDidBecomeActiveNotification` 通知
4. **MP3文件播放**: 读取app bundle中的 `1分钟无声空白音效.mp3` 文件
5. **循环播放**: 设置 `numberOfLoops = -1` 实现无限循环

## 注意事项

1. **文件要求**: 必须将 `1分钟无声空白音效.mp3` 文件添加到app bundle中
2. **权限要求**: 必须在 Info.plist 中添加 `audio` 后台模式
3. **音频会话**: 会影响其他音频应用的播放，使用了 `AVAudioSessionCategoryOptionMixWithOthers` 选项来减少冲突
4. **电池消耗**: 后台播放会增加电池消耗，但无声音频的消耗相对较小
5. **系统限制**: iOS系统可能会在某些情况下终止后台应用，这是系统行为

## 测试方法

1. 运行应用，查看控制台应该看到 "后台音频管理器已自动加载" 和 "后台音频管理器初始化完成"
2. 按Home键或锁屏，应用进入后台
3. 查看控制台日志，应该看到 "应用进入后台，开始播放无声音频"
4. 重新打开应用，应该看到 "应用变为活跃状态，停止播放无声音频"

## 优势

使用 `+load` 方法的优势：
1. **零配置**: 无需在 AppDelegate 或其他地方手动初始化
2. **自动启动**: 应用启动时自动加载，确保功能始终可用
3. **线程安全**: 使用 `dispatch_once` 确保单例的线程安全
4. **内存效率**: 全局只有一个实例，避免重复创建
