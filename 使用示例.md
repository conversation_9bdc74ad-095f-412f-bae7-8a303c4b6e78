# 后台音频播放使用示例

## 功能说明
这个 `avdome` 类实现了无限循环无声播放MP3文件来保持app在后台运行的功能。

## 主要特性
1. **自动检测应用状态**: 当应用进入后台时自动开始播放无声音频
2. **前台自动停止**: 当应用回到前台时立即停止播放
3. **无声播放**: 音量设置为0，不会影响用户体验
4. **无限循环**: 使用 `numberOfLoops = -1` 实现无限循环播放
5. **使用MP3文件**: 读取app bundle中的 `1分钟无声空白音效.mp3` 文件

## 使用方法

### 1. 在 AppDelegate 中初始化

```objc
#import "avdome.h"

@interface AppDelegate ()
@property (nonatomic, strong) avdome *backgroundAudioManager;
@end

@implementation AppDelegate

- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions {
    // 初始化后台音频管理器
    self.backgroundAudioManager = [[avdome alloc] init];
    
    return YES;
}

@end
```

### 2. 添加音频文件到项目

将 `1分钟无声空白音效.mp3` 文件添加到你的Xcode项目的bundle中：
1. 在Xcode中右键点击项目
2. 选择 "Add Files to [项目名]"
3. 选择 `1分钟无声空白音效.mp3` 文件
4. 确保 "Add to target" 勾选了你的app target

### 3. 在 Info.plist 中添加后台模式权限

在你的 Info.plist 文件中添加以下配置：

```xml
<key>UIBackgroundModes</key>
<array>
    <string>audio</string>
</array>
```

### 4. 手动控制（可选）

如果你需要手动控制播放：

```objc
// 开始后台播放
[self.backgroundAudioManager startBackgroundAudio];

// 停止后台播放
[self.backgroundAudioManager stopBackgroundAudio];
```

## 工作原理

1. **音频会话设置**: 使用 `AVAudioSessionCategoryPlayback` 类别允许后台播放
2. **应用状态监听**: 监听 `UIApplicationDidEnterBackgroundNotification` 和 `UIApplicationDidBecomeActiveNotification` 通知
3. **MP3文件播放**: 读取app bundle中的 `1分钟无声空白音效.mp3` 文件
4. **循环播放**: 设置 `numberOfLoops = -1` 实现无限循环

## 注意事项

1. **文件要求**: 必须将 `1分钟无声空白音效.mp3` 文件添加到app bundle中
2. **权限要求**: 必须在 Info.plist 中添加 `audio` 后台模式
3. **音频会话**: 会影响其他音频应用的播放，使用了 `AVAudioSessionCategoryOptionMixWithOthers` 选项来减少冲突
4. **电池消耗**: 后台播放会增加电池消耗，但无声音频的消耗相对较小
5. **系统限制**: iOS系统可能会在某些情况下终止后台应用，这是系统行为

## 测试方法

1. 运行应用
2. 按Home键或锁屏，应用进入后台
3. 查看控制台日志，应该看到 "应用进入后台，开始播放无声音频"
4. 重新打开应用，应该看到 "应用变为活跃状态，停止播放无声音频"
