//
//  avdome.h
//  真后台
//
//  Created by 小七 on 2025/7/30.
//

#import <Foundation/Foundation.h>
#import <AVFoundation/AVFoundation.h>
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface avdome : NSObject <AVAudioPlayerDelegate>

@property (nonatomic, strong) AVAudioPlayer *audioPlayer;
@property (nonatomic, assign) BOOL isInBackground;
@property (nonatomic, strong) NSTimer *progressTimer;

// 获取单例实例
+ (instancetype)sharedInstance;

// 开始后台播放
- (void)startBackgroundAudio;

// 停止后台播放
- (void)stopBackgroundAudio;

// 设置音频会话
- (void)setupAudioSession;

@end

NS_ASSUME_NONNULL_END
